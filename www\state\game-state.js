// state/game-state.js
// Centralized state management for the game

import { logger } from '../utils/logger.js';
import { config } from '../config.js';
import { storageService } from '../storage/storage-service.js';
import { pokemonManager } from '../services/pokemon-manager.js';

// Simple event system
class EventEmitter {
  constructor() {
    this.events = {};
  }

  on(event, listener) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(listener);
    return () => this.off(event, listener);
  }

  off(event, listener) {
    if (!this.events[event]) return;
    this.events[event] = this.events[event].filter(l => l !== listener);
  }

  emit(event, ...args) {
    if (!this.events[event]) return;
    this.events[event].forEach(listener => {
      try {
        listener(...args);
      } catch (e) {
        logger.error(`Error in event listener for ${event}:`, e);
      }
    });
  }
}

export class GameState {
  constructor() {
    // Map related
    this.map = null;
    this.compassMarker = null;
    this.compassArrow = null;
    this.compassLabel = null;
    this.catchRadiusCircle = null;

    // Pokemon related
    this.pokemons = [];
    this.pokemonMarkers = new Map(); // Map of id -> { marker, popupOpen }
    this.pokedex = [];
    this.pokedexData = [];
    this.basePokemonByChain = {};
    this.chainsList = [];

    // Trainer related
    this.trainers = [];
    this.trainerMarkers = new Map(); // Map of id -> { marker, popupOpen }

    // Location related
    this.lastUserLatLng = null;
    this.lastSpawnLatLng = null;
    this.lastGpsForHeading = null;
    this.gpsStabilizationTimer = null;
    this.lastMoveTimestamp = null;
    this.lastMovePosition = null;
    this.lastMovementHeading = 0;
    this.lastKnownHeading = 0;

    // UI state
    this.isCenteringActive = true;
    this.hasCenteredOnUser = false;
    this.shouldRecenterOnNextLocation = false;
    this.debugMode = false;

    // Storage sync control
    this.disableStorageSync = false;

    // Debug layers
    this.debugRadiusCircle = null;
    this.debugGridLayers = null;
    this.debugGridLabels = null;
    this.landuseCache = null;

    // Animation
    this.pokemonMoveInterval = null;

    // Event system
    this.events = new EventEmitter();

    logger.debug('GameState initialized');
  }

  /**
   * Initialize the game state
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Load pokedex from storage
      this.pokedex = await storageService.get(config.storage.pokedexKey, []);
      logger.info(`Loaded pokedex from storage - count: ${this.pokedex.length}`);

      // Load pokedex data
      await this.loadPokedexData();

      logger.info('GameState initialized successfully');
    } catch (e) {
      logger.error('Error initializing GameState:', e);
    }
  }

  /**
   * Load pokedex data from JSON files
   * @returns {Promise<void>}
   */
  async loadPokedexData() {
    try {
      // Load pokedex-151.json
      const response = await fetch('./pokedex-151.json');
      this.pokedexData = await response.json();

      // Create mapping: evolution_chain_id -> base Pokemon
      this.basePokemonByChain = {};
      const chainSet = new Set();

      for (const p of this.pokedexData) {
        if (!this.basePokemonByChain[p.evolution_chain_id]) {
          this.basePokemonByChain[p.evolution_chain_id] = p;
        }
        chainSet.add(p.evolution_chain_id);
      }

      this.chainsList = Array.from(chainSet).sort((a, b) => a - b);

      logger.info(`Pokedex data loaded - pokemonCount: ${this.pokedexData.length}, chainsCount: ${this.chainsList.length}`);

      // Notify listeners that data is loaded
      this.events.emit('pokedexDataLoaded', {
        pokedexData: this.pokedexData,
        chainsList: this.chainsList
      });
    } catch (e) {
      logger.error('Error loading pokedex data:', e);
    }
  }

  /**
   * Update the user's location
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   */
  updateUserLocation(lat, lng) {
    this.lastUserLatLng = { lat, lng };
    this.events.emit('locationUpdated', { lat, lng });
  }

  /**
   * Save the pokedex to storage
   * @returns {Promise<boolean>}
   */
  async savePokedex() {
    return await storageService.set(config.storage.pokedexKey, this.pokedex);
  }

  /**
   * Add a Pokemon to the pokedex
   * @param {Object} pokemon - The Pokemon to add
   * @param {boolean} addToCaughtStorage - Whether to add to caught storage (default: false)
   * @returns {Promise<boolean>}
   */
  async addToPokedex(pokemon, addToCaughtStorage = false) {
    try {
      // Add timestamp
      pokemon.discoveredAt = Date.now();

      // Add to pokedex array for discovery tracking
      const existingIndex = this.pokedex.findIndex(p => p.id === pokemon.id);
      if (existingIndex === -1) {
        this.pokedex.push(pokemon);
        logger.debug(`Added Pokemon ${pokemon.name} to pokedex for discovery tracking`);
      }

      const success = await this.savePokedex();

      // Only add to the central Pokemon manager if explicitly requested
      // This separates discovery from catching
      if (addToCaughtStorage) {
        // Add caught timestamp
        pokemon.caughtAt = Date.now();

        await pokemonManager.initialize();
        await pokemonManager.addPokemon(pokemon);
        logger.debug(`Added Pokemon ${pokemon.name} to central storage via addToPokedex`);
      }

      if (success) {
        this.events.emit('pokedexUpdated', this.pokedex);
      }
      return success;
    } catch (e) {
      logger.error('Error adding Pokemon to pokedex:', e);
      return false;
    }
  }

  /**
   * Add a Pokemon to the game world
   * @param {Object} pokemon - The Pokemon to add
   */
  addPokemon(pokemon) {
    this.pokemons.push(pokemon);
    this.events.emit('pokemonsUpdated', this.pokemons);
  }

  /**
   * Remove a Pokemon from the game world
   * @param {string} id - The ID of the Pokemon to remove
   * @returns {Object|null} - The removed Pokemon or null if not found
   */
  removePokemon(id) {
    const index = this.pokemons.findIndex(p => p.id === id);
    if (index === -1) return null;

    const pokemon = this.pokemons[index];
    this.pokemons.splice(index, 1);
    this.events.emit('pokemonsUpdated', this.pokemons);
    return pokemon;
  }

  /**
   * Set the debug mode
   * @param {boolean} enabled - Whether debug mode is enabled
   */
  setDebugMode(enabled) {
    this.debugMode = enabled;
    this.events.emit('debugModeChanged', enabled);
    logger.setDebugEnabled(enabled);
  }

  /**
   * Toggle the debug mode
   * @returns {boolean} - The new debug mode state
   */
  toggleDebugMode() {
    this.setDebugMode(!this.debugMode);
    return this.debugMode;
  }

  /**
   * Add a trainer to the game world
   * @param {Trainer} trainer - The trainer to add
   */
  addTrainer(trainer) {
    this.trainers.push(trainer);
    this.events.emit('trainersUpdated', this.trainers);
  }

  /**
   * Remove a trainer from the game world
   * @param {string} id - The ID of the trainer to remove
   * @returns {Trainer|null} - The removed trainer or null if not found
   */
  removeTrainer(id) {
    const index = this.trainers.findIndex(t => t.id === id);
    if (index === -1) return null;

    const trainer = this.trainers[index];
    this.trainers.splice(index, 1);
    this.events.emit('trainersUpdated', this.trainers);
    return trainer;
  }
}

// Export a singleton instance
export const gameState = new GameState();
