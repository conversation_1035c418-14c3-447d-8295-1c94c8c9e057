// storage/storage-service.js
// Unified storage service that works with both Capacitor Storage and localStorage

import { logger } from '../utils/logger.js';

export class StorageService {
  constructor() {
    this.capacitorAvailable = !!(window.Capacitor?.Plugins?.Storage);
    logger.debug('StorageService initialized', { capacitorAvailable: this.capacitorAvailable });
  }
  
  /**
   * Get a value from storage
   * @param {string} key - The key to get
   * @param {any} defaultValue - The default value if the key doesn't exist
   * @returns {Promise<any>} - The value from storage
   */
  async get(key, defaultValue = null) {
    try {
      if (this.capacitorAvailable) {
        const result = await window.Capacitor.Plugins.Storage.get({ key });
        return result.value ? JSON.parse(result.value) : defaultValue;
      } else {
        const value = localStorage.getItem(key);
        return value ? JSON.parse(value) : defaultValue;
      }
    } catch (e) {
      logger.error(`<PERSON>rror getting ${key} from storage:`, e);
      return defaultValue;
    }
  }
  
  /**
   * Set a value in storage
   * @param {string} key - The key to set
   * @param {any} value - The value to set
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async set(key, value) {
    try {
      const jsonValue = JSON.stringify(value);
      if (this.capacitorAvailable) {
        await window.Capacitor.Plugins.Storage.set({ key, value: jsonValue });
      } else {
        localStorage.setItem(key, jsonValue);
      }
      logger.debug(`Saved to storage: ${key}`);
      return true;
    } catch (e) {
      logger.error(`Error setting ${key} in storage:`, e);

      // If quota exceeded, try to clean up old data
      if (e.name === 'QuotaExceededError' || e.message.includes('quota')) {
        logger.warn('Storage quota exceeded, attempting cleanup...');
        await this.cleanupOldData();

        // Try again after cleanup
        try {
          const jsonValue = JSON.stringify(value);
          if (this.capacitorAvailable) {
            await window.Capacitor.Plugins.Storage.set({ key, value: jsonValue });
          } else {
            localStorage.setItem(key, jsonValue);
          }
          logger.info(`Successfully saved ${key} after cleanup`);
          return true;
        } catch (retryError) {
          logger.error(`Failed to save ${key} even after cleanup:`, retryError);
          return false;
        }
      }

      return false;
    }
  }
  
  /**
   * Remove a value from storage
   * @param {string} key - The key to remove
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async remove(key) {
    try {
      if (this.capacitorAvailable) {
        await window.Capacitor.Plugins.Storage.remove({ key });
      } else {
        localStorage.removeItem(key);
      }
      logger.debug(`Removed from storage: ${key}`);
      return true;
    } catch (e) {
      logger.error(`Error removing ${key} from storage:`, e);
      return false;
    }
  }
  
  /**
   * Clear all values from storage
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async clear() {
    try {
      if (this.capacitorAvailable) {
        await window.Capacitor.Plugins.Storage.clear();
      } else {
        localStorage.clear();
      }
      logger.debug('Storage cleared');
      return true;
    } catch (e) {
      logger.error('Error clearing storage:', e);
      return false;
    }
  }
  
  /**
   * Get all keys from storage
   * @returns {Promise<string[]>} - The keys in storage
   */
  async keys() {
    try {
      if (this.capacitorAvailable) {
        const { keys } = await window.Capacitor.Plugins.Storage.keys();
        return keys;
      } else {
        return Object.keys(localStorage);
      }
    } catch (e) {
      logger.error('Error getting keys from storage:', e);
      return [];
    }
  }

  /**
   * Clean up old data to free storage space
   * @returns {Promise<void>}
   */
  async cleanupOldData() {
    try {
      logger.info('Starting storage cleanup...');

      if (this.capacitorAvailable) {
        // Capacitor cleanup
        const { keys } = await window.Capacitor.Plugins.Storage.keys();
        const keysToRemove = [];

        // Remove old cache entries (older than 24 hours)
        const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);

        for (const key of keys) {
          if (key.startsWith('osm_ways_') || key.startsWith('landuse_cache_')) {
            try {
              const { value } = await window.Capacitor.Plugins.Storage.get({ key });
              if (value) {
                const data = JSON.parse(value);
                if (data.timestamp && data.timestamp < oneDayAgo) {
                  keysToRemove.push(key);
                }
              }
            } catch (e) {
              // If we can't parse it, remove it
              keysToRemove.push(key);
            }
          }
        }

        // Remove old keys
        for (const key of keysToRemove) {
          await window.Capacitor.Plugins.Storage.remove({ key });
        }

        logger.info(`Cleaned up ${keysToRemove.length} old cache entries`);

      } else {
        // localStorage cleanup
        const keysToRemove = [];
        const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);

        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.startsWith('osm_ways_') || key.startsWith('landuse_cache_'))) {
            try {
              const value = localStorage.getItem(key);
              if (value) {
                const data = JSON.parse(value);
                if (data.timestamp && data.timestamp < oneDayAgo) {
                  keysToRemove.push(key);
                }
              }
            } catch (e) {
              // If we can't parse it, remove it
              keysToRemove.push(key);
            }
          }
        }

        // Remove old keys
        keysToRemove.forEach(key => localStorage.removeItem(key));

        logger.info(`Cleaned up ${keysToRemove.length} old localStorage entries`);
      }

    } catch (error) {
      logger.error('Error during storage cleanup:', error);
    }
  }

  /**
   * Get storage usage information
   * @returns {Promise<Object>} Storage usage stats
   */
  async getStorageInfo() {
    try {
      if (this.capacitorAvailable) {
        const { keys } = await window.Capacitor.Plugins.Storage.keys();
        return {
          totalKeys: keys.length,
          cacheKeys: keys.filter(k => k.startsWith('osm_ways_') || k.startsWith('landuse_cache_')).length,
          gameDataKeys: keys.filter(k => k === 'timeEventSpawns' || k === 'gameState').length
        };
      } else {
        const totalKeys = localStorage.length;
        const cacheKeys = [];
        const gameDataKeys = [];

        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key) {
            if (key.startsWith('osm_ways_') || key.startsWith('landuse_cache_')) {
              cacheKeys.push(key);
            } else if (key === 'timeEventSpawns' || key === 'gameState') {
              gameDataKeys.push(key);
            }
          }
        }

        return {
          totalKeys,
          cacheKeys: cacheKeys.length,
          gameDataKeys: gameDataKeys.length
        };
      }
    } catch (error) {
      logger.error('Error getting storage info:', error);
      return { totalKeys: 0, cacheKeys: 0, gameDataKeys: 0 };
    }
  }
}

// Export a singleton instance
export const storageService = new StorageService();
