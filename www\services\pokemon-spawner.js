// services/pokemon-spawner.js
// Service for spawning Pokemon in the game world

import { config } from '../config.js';
import { logger } from '../utils/logger.js';
import { gameState } from '../state/game-state.js';
import Pokemon from '../Pokemon.js';
import { getChainIndexForLocation } from '../pokemon-grid.js';
import { getLandusePolygonsGeoJSON, getLanduseForLatLng } from '../overpass-landuse.js';
import { LANDUSE_TYPE_MAPPING } from '../landuse-pokemon-types.js';
import { getSpawnLevel as calculateSpawnLevel } from './spawn-levels.js';
import { updatePokemonFromPokedex } from '../utils/pokemon-utils.js';

// Import Turf.js for geospatial operations
import turf from '../lib/turf.js';

/**
 * Calculate distance in meters between two lat/lng points
 * @param {number} lat1 - Latitude of point 1
 * @param {number} lng1 - Longitude of point 1
 * @param {number} lat2 - Latitude of point 2
 * @param {number} lng2 - Longitude of point 2
 * @returns {number} - Distance in meters
 */
export function distanceMeters(lat1, lng1, lat2, lng2) {
  function toRad(x) { return x * Math.PI / 180; }
  const R = 6371000;
  const dLat = toRad(lat2 - lat1);
  const dLng = toRad(lng2 - lng1);
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) *
            Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * Calculate azimuth (direction) between two points
 * @param {number} lat1 - Latitude of point 1
 * @param {number} lng1 - Longitude of point 1
 * @param {number} lat2 - Latitude of point 2
 * @param {number} lng2 - Longitude of point 2
 * @returns {number} - Azimuth in degrees (0-360)
 */
export function calculateAzimuth(lat1, lng1, lat2, lng2) {
  const toRad = deg => deg * Math.PI / 180;
  const toDeg = rad => rad * 180 / Math.PI;
  const dLon = toRad(lng2 - lng1);
  const y = Math.sin(dLon) * Math.cos(toRad(lat2));
  const x = Math.cos(toRad(lat1)) * Math.sin(toRad(lat2)) - Math.sin(toRad(lat1)) * Math.cos(toRad(lat2)) * Math.cos(dLon);
  let brng = Math.atan2(y, x);
  brng = toDeg(brng);
  return (brng + 360) % 360;
}

/**
 * Generate a random offset in a specific direction
 * @param {number} maxMeters - Maximum distance in meters
 * @param {number} baseLat - Base latitude
 * @param {number} baseLng - Base longitude
 * @param {number} lastLat - Last latitude (for directional spawning)
 * @param {number} lastLng - Last longitude (for directional spawning)
 * @param {number} sectorDeg - Sector angle in degrees
 * @returns {Object} - { lat, lng } offset
 */
function randomOffsetInMetersDirectional(maxMeters, baseLat, baseLng, lastLat, lastLng, sectorDeg = 120) {
  // Determine direction
  let angle = Math.random() * 2 * Math.PI; // Default: all directions allowed
  if (typeof lastLat === 'number' && typeof lastLng === 'number' && (baseLat !== lastLat || baseLng !== lastLng)) {
    // Direction: from last spawn point to current position
    const dx = baseLng - lastLng;
    const dy = baseLat - lastLat;
    const dir = Math.atan2(dy, dx); // Direction in radians
    // Sector angle in radians
    const sector = (sectorDeg / 180) * Math.PI;
    const offset = (Math.random() - 0.5) * sector; // e.g. ±60°
    angle = dir + offset;
  }
  const distance = Math.random() * maxMeters;
  // Convert meters to degrees
  const dLat = Math.cos(angle) * (distance / 111320);
  const dLng = Math.sin(angle) * (distance / (111320 * Math.cos(baseLat * Math.PI / 180)));
  return {
    lat: dLat,
    lng: dLng
  };
}

export class PokemonSpawner {
  constructor() {
    this.spawnRadius = config.pokemon.spawnRadius;
    this.spawnDistanceTrigger = config.pokemon.spawnDistanceTrigger;
    this.spawnBatchSize = config.pokemon.spawnBatchSize;
    this.landuseSpecialBatchSize = config.pokemon.landuseSpecialBatchSize;
    this.maxPokemons = config.pokemon.maxPokemons;

    // Rarity probabilities for Pokémon spawning
    this.rarityProbabilities = {
      common: 0.65,    // 65%
      scarce: 0.22,    // 22%
      rare: 0.08,      // 8%
      legendary: 0.04, // 4%
      mythical: 0.01   // 1%
    };

    // Landuse area cache for performance optimization
    this.landuseCache = null;

    // Async loading synchronization to prevent race conditions
    this.isLoadingLanduse = false;
    this.landuseLoadingPromise = null;
  }

  /**
   * Load all landuse data once and cache it for the spawn session
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @param {number} radius - Radius in meters
   * @returns {Promise<Object>} - GeoJSON FeatureCollection
   */
  async loadLanduseDataForArea(lat, lng, radius) {
    const cacheKey = `${Math.round(lat * 1000)}:${Math.round(lng * 1000)}:${radius}`;

    if (this.landuseCache && this.landuseCache.key === cacheKey) {
      logger.debug(`Using cached landuse data for area: ${cacheKey}`);
      return this.landuseCache.data;
    }

    logger.debug(`Loading landuse data for area: ${cacheKey}`);

    // Load all landuse polygons once
    const geojson = await getLandusePolygonsGeoJSON(lat, lng, radius);

    this.landuseCache = {
      key: cacheKey,
      data: geojson,
      timestamp: Date.now()
    };

    logger.debug(`Cached landuse data with ${geojson.features.length} features for area: ${cacheKey}`);
    return geojson;
  }

  /**
   * Fast landuse lookup using cached data (only valid landuse types)
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @param {boolean} allowFallback - Whether to use fallback landuse assignment
   * @returns {Object|null} - Landuse data or null
   */
  getLanduseForPoint(lat, lng, allowFallback = false) {
    logger.info(`[LANDUSE_LOOKUP] Starting landuse lookup for point ${lat}, ${lng} (fallback: ${allowFallback})`);

    if (!this.landuseCache?.data?.features) {
      logger.info(`[LANDUSE_LOOKUP] No landuse cache available for point ${lat}, ${lng}`);
      return allowFallback ? this.getFallbackLanduse(lat, lng) : null;
    }

    const point = turf.point([lng, lat]);
    logger.info(`[LANDUSE_LOOKUP] Checking ${this.landuseCache.data.features.length} landuse features for point ${lat}, ${lng}`);

    // Debug: Count forest features in cache
    const forestFeaturesInCache = this.landuseCache.data.features.filter(f => f.properties && (f.properties.value === 'forest' || f.properties.value === 'wood'));
    logger.info(`[LANDUSE_LOOKUP] 🌲 Forest features in cache: ${forestFeaturesInCache.length}`);

    // Find the polygon containing this point
    for (const feature of this.landuseCache.data.features) {
      try {
        if (turf.booleanPointInPolygon(point, feature)) {
          const landuseType = feature.properties.value;

          // ✅ Only return valid landuse types that have a mapping
          if (LANDUSE_TYPE_MAPPING[landuseType]) {
            logger.info(`[LANDUSE_LOOKUP] ✅ Found valid landuse match: ${landuseType} for point ${lat}, ${lng}`);
            return {
              value: landuseType
            };
          } else {
            logger.info(`[LANDUSE_LOOKUP] ⚠️ Found landuse ${landuseType} but no mapping available - skipping`);
            continue; // Continue searching for valid landuse types
          }
        }
      } catch (e) {
        // Skip invalid polygons
        continue;
      }
    }

    logger.info(`[LANDUSE_LOOKUP] ❌ No valid landuse match found for point ${lat}, ${lng}`);
    return allowFallback ? this.getFallbackLanduse(lat, lng) : null;
  }

  /**
   * Get fallback landuse for areas without explicit OSM landuse data
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @returns {Object} - Fallback landuse information
   */
  getFallbackLanduse(lat, lng) {
    // Intelligent fallback based on geographic context
    // In rural areas without explicit landuse, assume common rural landuse types
    const fallbackTypes = [
      { type: 'grassland', weight: 30 },    // Natürliche Grasflächen
      { type: 'meadow', weight: 25 },       // Wiesen
      { type: 'farmland', weight: 20 },     // Landwirtschaft
      { type: 'forest', weight: 15 },       // Wald
      { type: 'scrub', weight: 10 }         // Gestrüpp/Buschland
    ];

    // Weighted random selection
    const totalWeight = fallbackTypes.reduce((sum, item) => sum + item.weight, 0);
    const random = Math.random() * totalWeight;
    let currentWeight = 0;

    for (const fallback of fallbackTypes) {
      currentWeight += fallback.weight;
      if (random <= currentWeight) {
        logger.info(`[LANDUSE_FALLBACK] 🎯 Assigned fallback landuse '${fallback.type}' at ${lat.toFixed(6)}, ${lng.toFixed(6)}`);

        return {
          value: fallback.type,
          fallback: true // Mark as fallback
        };
      }
    }

    // Should never reach here, but fallback to grassland
    logger.info(`[LANDUSE_FALLBACK] 🎯 Default fallback to 'grassland' at ${lat.toFixed(6)}, ${lng.toFixed(6)}`);
    return {
      value: 'grassland',
      fallback: true
    };
  }

  /**
   * Recreate Pokemon from stored data
   * @param {Array} storedPokemons - Array of stored Pokemon data
   * @returns {Array} - Array of recreated Pokemon objects
   */
  recreateStoredPokemons(storedPokemons) {
    const recreated = [];

    if (!storedPokemons || !Array.isArray(storedPokemons) || storedPokemons.length === 0) {
      logger.debug('No stored Pokemon to recreate');
      return recreated;
    }

    logger.info(`Recreating ${storedPokemons.length} Pokemon from storage`);

    // Ensure pokedex data is loaded
    const pokedexData = gameState.pokedexData;
    if (!pokedexData || pokedexData.length === 0) {
      logger.warn('Pokedex data not loaded, Pokemon may have missing dex_number values');
    }

    for (const storedPokemon of storedPokemons) {
      try {
        // Use Pokemon.fromJSON to properly recreate the Pokemon with all properties
        const pokemon = Pokemon.fromJSON(storedPokemon);

        // Ensure spawn location properties are set
        if (storedPokemon.spawnLat && storedPokemon.spawnLng) {
          pokemon.spawnLat = storedPokemon.spawnLat;
          pokemon.spawnLng = storedPokemon.spawnLng;
          pokemon.lat = storedPokemon.lat || storedPokemon.spawnLat;
          pokemon.lng = storedPokemon.lng || storedPokemon.spawnLng;
        }

        // Ensure landuse properties are set
        pokemon.landuseSpecial = storedPokemon.landuseSpecial || false;
        pokemon.landuseType = storedPokemon.landuseType || null;
        pokemon.landuseTypeName = storedPokemon.landuseTypeName || null;
        pokemon.featureId = storedPokemon.featureId || null;

        // We don't automatically set landuseSpecial based on landuseType
        // Only Pokemon that were originally spawned as special landuse Pokemon should have landuseSpecial=true

        // Update Pokemon with data from pokedex if needed
        if (!pokemon.dex_number || !pokemon.types || !pokemon.types.length || !pokemon.image) {
          const updated = updatePokemonFromPokedex(pokemon, pokedexData);
          if (updated) {
            logger.debug(`Updated Pokemon ${pokemon.name} with data from pokedex during recreation`);
          }
        }

        // If image is still not set but dex_number is available, set image based on dex_number
        if (!pokemon.image && pokemon.dex_number) {
          pokemon.image = `./src/PokemonSprites/${pokemon.dex_number}.png`;
        }

        // Log successful recreation with dex_number for debugging
        logger.debug(`Recreated Pokemon ${pokemon.name} (ID: ${pokemon.id}) with dex_number: ${pokemon.dex_number}`);

        // Add to game state
        gameState.addPokemon(pokemon);
        recreated.push(pokemon);
      } catch (e) {
        logger.error('Error recreating Pokemon from storage:', e, storedPokemon);
      }
    }

    logger.info(`Successfully recreated ${recreated.length} Pokemon from storage`);
    return recreated;
  }

  /**
   * Collect Pokemon data for spawning (parallel data collection)
   * @param {number} lat - Base latitude
   * @param {number} lng - Base longitude
   * @param {boolean} useDirectionalSpawn - Whether to use directional spawning
   * @param {number|null} movementAzimuth - Direction of movement in degrees
   * @param {Array} pokedexSnapshot - Isolated pokedex data snapshot
   * @returns {Promise<Object|null>} - Collected Pokemon data or null if failed
   */
  async collectPokemonData(lat, lng, useDirectionalSpawn, movementAzimuth, pokedexSnapshot) {
    try {
      // Calculate spawn location
      const spawnLocation = this.calculateSpawnLocation(lat, lng, useDirectionalSpawn, movementAzimuth);

      // Get base Pokemon for this grid location
      const basePokeEntry = this.getBasePokemonForGrid(spawnLocation.lat, spawnLocation.lng);
      if (!basePokeEntry) return null;

      // Get spawn level based on player's team average level
      const level = await this.getSpawnLevel();

      // Calculate evolution data with isolated pokedex snapshot
      const evolutionData = await this.calculateEvolutionData(basePokeEntry.name, level, pokedexSnapshot);

      // Get landuse data from cache (fast lookup with fallback)
      logger.debug(`[LANDUSE_DEBUG] Starting landuse lookup for ${basePokeEntry.name} at ${spawnLocation.lat},${spawnLocation.lng}`);
      const landuseData = this.getLanduseForPoint(spawnLocation.lat, spawnLocation.lng, true); // Enable fallback
      logger.debug(`[LANDUSE_DEBUG] Landuse lookup completed for ${basePokeEntry.name}:`);
      logger.debug(`[LANDUSE_DEBUG] - Cache available: ${this.landuseCache ? 'YES' : 'NO'}`);
      logger.debug(`[LANDUSE_DEBUG] - Cache features: ${this.landuseCache?.data?.features?.length || 0}`);
      logger.debug(`[LANDUSE_DEBUG] - Landuse result: ${landuseData ? landuseData.value : 'null'}`);
      logger.debug(`[LANDUSE_DEBUG] - Is fallback: ${landuseData?.fallback ? 'YES' : 'NO'}`);

      // Return all collected data
      return {
        basePokeEntry,
        level,
        evolutionData,
        spawnLocation,
        landuseData,
        timestamp: Date.now() // For debugging
      };
    } catch (e) {
      logger.error('Error collecting Pokemon data:', e);
      return null;
    }
  }

  /**
   * Calculate spawn location
   * @param {number} lat - Base latitude
   * @param {number} lng - Base longitude
   * @param {boolean} useDirectionalSpawn - Whether to use directional spawning
   * @param {number|null} movementAzimuth - Direction of movement in degrees
   * @returns {Object} - {lat, lng} spawn location
   */
  calculateSpawnLocation(lat, lng, useDirectionalSpawn, movementAzimuth) {
    try {
      if (useDirectionalSpawn && movementAzimuth !== null) {
        // Directional spawn - use turf.js destination
        // Convert sector angle to radians for random offset
        const sectorAngle = config.ui.debugSectorAngle;
        const randomAngleOffset = ((Math.random() - 0.5) * sectorAngle);
        const distance = Math.random() * this.spawnRadius;

        // Calculate destination point in the direction of movement with random offset
        const dest = turf.destination(
          [lng, lat],
          distance / 1000, // turf uses km
          movementAzimuth + randomAngleOffset
        );

        return {
          lat: dest.geometry.coordinates[1],
          lng: dest.geometry.coordinates[0]
        };
      } else {
        // Non-directional spawn (360 degrees)
        const randomAngle = Math.random() * 360;
        const distance = Math.random() * this.spawnRadius;

        const dest = turf.destination(
          [lng, lat],
          distance / 1000, // turf uses km
          randomAngle
        );

        return {
          lat: dest.geometry.coordinates[1],
          lng: dest.geometry.coordinates[0]
        };
      }
    } catch (e) {
      logger.error('Error using turf.js for spawn location calculation:', e);
      // Fallback to non-directional if turf fails
      const offset = randomOffsetInMetersDirectional(this.spawnRadius, lat, lng, lat, lng, 360);
      return {
        lat: lat + offset.lat,
        lng: lng + offset.lng
      };
    }
  }

  /**
   * Calculate evolution data with isolated pokedex
   * @param {string} baseName - Base Pokemon name
   * @param {number} level - Pokemon level
   * @param {Array} pokedexSnapshot - Isolated pokedex data snapshot
   * @returns {Promise<Object>} - Evolution data {name, sprite, dex_number, types}
   */
  async calculateEvolutionData(baseName, level, pokedexSnapshot) {
    // Validate input parameters
    if (!baseName || typeof baseName !== 'string') {
      throw new Error(`Invalid baseName for evolution calculation: ${baseName}`);
    }
    if (!level || typeof level !== 'number' || level < 1 || level > 100) {
      throw new Error(`Invalid level for evolution calculation: ${level}`);
    }
    if (!pokedexSnapshot || !Array.isArray(pokedexSnapshot) || pokedexSnapshot.length === 0) {
      throw new Error(`Invalid pokedex snapshot for evolution calculation: ${pokedexSnapshot?.length || 'null'} entries`);
    }

    // Create temporary Pokemon object only for evolution calculation
    const tempPokemon = new Pokemon(baseName, 'normal', level);

    // Calculate evolution with isolated pokedex
    const displayForm = await tempPokemon.getDisplayForm(pokedexSnapshot);

    // Validate evolution result
    if (!displayForm || !displayForm.name || !displayForm.dex_number) {
      logger.error(`Invalid evolution result for ${baseName} level ${level}`);

      // Fallback to base Pokemon data
      const baseEntry = pokedexSnapshot.find(p =>
        p.name.toLowerCase() === baseName.toLowerCase()
      );

      if (baseEntry) {
        return {
          name: baseEntry.name,
          sprite: baseEntry.image_url,
          dex_number: baseEntry.dex_number,
          types: baseEntry.types
        };
      } else {
        // Ultimate fallback
        return {
          name: baseName,
          sprite: '',
          dex_number: null,
          types: ['normal']
        };
      }
    }

    return {
      name: displayForm.name,
      sprite: displayForm.sprite,
      dex_number: displayForm.dex_number,
      types: displayForm.types,
      evolution_chain_id: displayForm.evolution_chain_id
    };
  }

  /**
   * Get landuse data for location
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @returns {Promise<Object|null>} - Landuse data or null
   */
  async getLanduseData(lat, lng) {
    try {
      const landuseData = await getLanduseForLatLng(lat, lng);
      if (landuseData && landuseData.value) {
        return {
          value: landuseData.value
        };
      }
    } catch (e) {
      logger.error('Error getting landuse data:', e);
    }
    return null;
  }

  /**
   * Create final Pokemon object with pre-calculated data (base method)
   * @param {Object} pokemonData - Pre-calculated Pokemon data
   * @param {boolean} isLanduseSpecial - Whether this is a landuse special Pokemon
   * @returns {Pokemon|null} - Final Pokemon object or null
   */
  createFinalPokemonBase(pokemonData, isLanduseSpecial = false) {
    try {
      // Validate input data
      if (!pokemonData || !pokemonData.basePokeEntry || !pokemonData.evolutionData) {
        throw new Error('Invalid Pokemon data provided');
      }

      // Create Pokemon with BASE name first, then set evolution data
      const pokemon = new Pokemon(
        pokemonData.basePokeEntry.name,
        pokemonData.basePokeEntry.types[0],
        pokemonData.level
      );

      // Apply common evolution data
      this.applyEvolutionData(pokemon, pokemonData);

      // Apply common location data
      this.applyLocationData(pokemon, pokemonData);

      // Apply landuse-specific data if needed
      if (isLanduseSpecial) {
        this.applyLanduseSpecialData(pokemon, pokemonData);
      } else {
        this.applyStandardSpawnData(pokemon, pokemonData);
      }

      // Final fallback for image if still not set
      if (!pokemon.image && pokemon.dex_number) {
        pokemon.image = `./src/PokemonSprites/${pokemon.dex_number}.png`;
      }

      logger.debug(`Created final Pokemon: ${pokemon.name} (base: ${pokemon.base_name}) Level ${pokemon.level}, Dex #${pokemon.dex_number}${isLanduseSpecial ? ' [Landuse Special]' : ''}`);
      return pokemon;
    } catch (e) {
      logger.error('Error creating final Pokemon:', e, pokemonData);
      return null;
    }
  }

  /**
   * Apply evolution data to Pokemon object
   * @param {Pokemon} pokemon - Pokemon object to modify
   * @param {Object} pokemonData - Pokemon data containing evolution info
   */
  applyEvolutionData(pokemon, pokemonData) {
    // Validate inputs
    if (!pokemon || typeof pokemon !== 'object') {
      throw new Error('Invalid Pokemon object provided to applyEvolutionData');
    }
    if (!pokemonData?.basePokeEntry || !pokemonData?.evolutionData) {
      throw new Error('Invalid Pokemon data provided to applyEvolutionData');
    }

    // Set base properties first
    pokemon.base_name = pokemonData.basePokeEntry.name;
    pokemon.base_sprite = pokemonData.basePokeEntry.image_url;
    pokemon.rarity = pokemonData.basePokeEntry.rarity || 'common';

    // Now set the evolved form data (this is the key fix!)
    // Keep all names in English for consistent internal logic
    pokemon.name = pokemonData.evolutionData.name; // English evolved name (e.g., "wartortle")
    pokemon.image_url = pokemonData.evolutionData.sprite;
    pokemon.image = pokemonData.evolutionData.sprite; // Ensure image is set
    pokemon.dex_number = pokemonData.evolutionData.dex_number;
    pokemon.types = pokemonData.evolutionData.types;

    // Set evolution chain ID to prevent getDisplayForm from being called again
    if (pokemonData.evolutionData.evolution_chain_id) {
      pokemon.evolution_chain_id = pokemonData.evolutionData.evolution_chain_id;
    } else if (pokemonData.basePokeEntry.evolution_chain_id) {
      pokemon.evolution_chain_id = pokemonData.basePokeEntry.evolution_chain_id;
    }
  }

  /**
   * Apply location data to Pokemon object
   * @param {Pokemon} pokemon - Pokemon object to modify
   * @param {Object} pokemonData - Pokemon data containing location info
   */
  applyLocationData(pokemon, pokemonData) {
    // Validate inputs
    if (!pokemon || typeof pokemon !== 'object') {
      throw new Error('Invalid Pokemon object provided to applyLocationData');
    }
    if (!pokemonData?.spawnLocation ||
        typeof pokemonData.spawnLocation.lat !== 'number' ||
        typeof pokemonData.spawnLocation.lng !== 'number') {
      throw new Error('Invalid spawn location data provided to applyLocationData');
    }

    pokemon.spawnLat = pokemonData.spawnLocation.lat;
    pokemon.spawnLng = pokemonData.spawnLocation.lng;
    pokemon.lat = pokemonData.spawnLocation.lat;
    pokemon.lng = pokemonData.spawnLocation.lng;
  }

  /**
   * Apply landuse-specific data to Pokemon object
   * @param {Pokemon} pokemon - Pokemon object to modify
   * @param {Object} pokemonData - Pokemon data containing landuse info
   */
  applyLanduseSpecialData(pokemon, pokemonData) {
    // Validate inputs
    if (!pokemon || typeof pokemon !== 'object') {
      throw new Error('Invalid Pokemon object provided to applyLanduseSpecialData');
    }
    if (!pokemonData?.landuseData?.value || !pokemonData?.landuseData?.feature?.properties?.osm_id) {
      throw new Error('Invalid landuse data provided to applyLanduseSpecialData');
    }

    pokemon.landuseSpecial = true;
    pokemon.landuseType = pokemonData.landuseData.value;
    pokemon.landuseTypeName = pokemonData.landuseData.value;
    pokemon.featureId = pokemonData.landuseData.feature.properties.osm_id;
  }

  /**
   * Apply standard spawn data to Pokemon object
   * @param {Pokemon} pokemon - Pokemon object to modify
   * @param {Object} pokemonData - Pokemon data containing landuse info
   */
  applyStandardSpawnData(pokemon, pokemonData) {
    // Set landuse data if available (for standard spawns)
    logger.debug(`[CACHE_DEBUG] Applying standard spawn data for ${pokemon.name}:`);
    logger.debug(`[CACHE_DEBUG] - pokemonData.landuseData = ${pokemonData.landuseData ? JSON.stringify(pokemonData.landuseData) : 'null'}`);
    logger.debug(`[CACHE_DEBUG] - spawn location = ${pokemonData.spawnLocation ? `${pokemonData.spawnLocation.lat},${pokemonData.spawnLocation.lng}` : 'null'}`);

    if (pokemonData.landuseData && pokemonData.landuseData.value) {
      pokemon.landuseTypeName = pokemonData.landuseData.value;
      pokemon.landuseType = pokemonData.landuseData.value;

      // Mark if this is a fallback landuse assignment
      if (pokemonData.landuseData.fallback) {
        pokemon.landuseFallback = true;
        logger.debug(`[CACHE_DEBUG] ✓ Set FALLBACK landuse data for ${pokemon.name}: ${pokemon.landuseTypeName}`);
      } else {
        pokemon.landuseFallback = false;
        logger.debug(`[CACHE_DEBUG] ✓ Set REAL landuse data for ${pokemon.name}: ${pokemon.landuseTypeName}`);
      }

      // For standard spawns, landuseSpecial remains false (default)
    } else {
      logger.debug(`[CACHE_DEBUG] ✗ No landuse data available for ${pokemon.name} - this should not happen with fallback enabled`);
    }
  }

  /**
   * Create final Pokemon object with pre-calculated data
   * @param {Object} pokemonData - Pre-calculated Pokemon data
   * @returns {Pokemon|null} - Final Pokemon object or null
   */
  createFinalPokemon(pokemonData) {
    return this.createFinalPokemonBase(pokemonData, false);
  }

  /**
   * Spawn random Pokemon around a location (with isolated data collection)
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @param {number} count - Number of Pokemon to spawn
   * @returns {Promise<Array>} - The spawned Pokemon
   */
  async spawnRandomPokemons(lat, lng, count = this.spawnBatchSize) {
    logger.info(`[STANDARD_SPAWN] 🎯 Starting spawnRandomPokemons with count=${count} at ${lat},${lng}`);
    const { pokemons } = gameState;

    // Remove oldest if exceeding max
    if (pokemons.length + count > this.maxPokemons) {
      const toRemove = (pokemons.length + count) - this.maxPokemons;
      for (let i = 0; i < toRemove; i++) {
        if (pokemons.length > 0) {
          const removed = pokemons.shift();
          if (removed && gameState.pokemonMarkers.has(removed.id)) {
            gameState.map.removeLayer(gameState.pokemonMarkers.get(removed.id).marker);
            gameState.pokemonMarkers.delete(removed.id);
          }
        }
      }
    }

    // 1. Create Pokedex snapshot to prevent race conditions
    const pokedexSnapshot = JSON.parse(JSON.stringify(gameState.pokedexData));
    logger.debug(`Created pokedex snapshot with ${pokedexSnapshot.length} entries`);

    // 2. Load comprehensive landuse data for 10km area to capture even larger forest polygons
    const landuseRadius = 10000; // 10km radius to capture very large polygons extending far beyond immediate area
    const cacheKey = `${Math.round(lat * 1000)}:${Math.round(lng * 1000)}:${landuseRadius}`;

    // Performance tracking for 5km radius impact
    const performanceStart = performance.now();
    const memoryBefore = performance.memory ? performance.memory.usedJSHeapSize : 0;

    // Implement proper async loading synchronization
    if (!this.landuseCache || this.landuseCache.key !== cacheKey) {
      // Check if already loading the same data
      if (this.isLoadingLanduse && this.landuseLoadingPromise) {
        logger.info(`[LANDUSE_CACHE] ⏳ Waiting for ongoing landuse loading to complete...`);
        await this.landuseLoadingPromise;

        // Check if the data we need is now available
        if (this.landuseCache && this.landuseCache.key === cacheKey) {
          logger.info(`[LANDUSE_CACHE] ✅ Landuse data loaded by parallel process`);
        } else {
          logger.warn(`[LANDUSE_CACHE] ⚠️ Parallel loading completed but cache key mismatch`);
        }
      } else {
        // Start new loading process
        this.isLoadingLanduse = true;
        logger.info(`[LANDUSE_CACHE] 🗺️ Loading comprehensive landuse cache for 10km area: ${lat}, ${lng}`);
        logger.info(`[LANDUSE_CACHE] 📡 Fetching ways + relations for landuse/natural/leisure from Overpass API...`);
        logger.info(`[PERFORMANCE] 🚀 Starting 10km radius landuse data loading...`);

        try {
          this.landuseLoadingPromise = this.loadLanduseDataForArea(lat, lng, landuseRadius);
          await this.landuseLoadingPromise;
          this.landuseCache.key = cacheKey;
          logger.info(`[LANDUSE_CACHE] ✅ Landuse cache loaded with ${this.landuseCache?.data?.features?.length || 0} total features`);
        } finally {
          this.isLoadingLanduse = false;
          this.landuseLoadingPromise = null;
        }
      }
    } else {
      logger.debug(`[LANDUSE_CACHE] 💾 Using existing landuse cache for area: ${cacheKey}`);
    }

    // Performance analysis for 5km radius impact
    const performanceEnd = performance.now();
    const memoryAfter = performance.memory ? performance.memory.usedJSHeapSize : 0;
    const loadTime = performanceEnd - performanceStart;
    const memoryDelta = memoryAfter - memoryBefore;

    logger.info(`[PERFORMANCE] ⏱️ 10km landuse loading completed in ${loadTime.toFixed(1)}ms`);
    logger.info(`[PERFORMANCE] 💾 Memory impact: ${(memoryDelta / 1024 / 1024).toFixed(2)}MB`);
    logger.info(`[PERFORMANCE] 📊 Features loaded: ${this.landuseCache?.data?.features?.length || 0}`);
    logger.info(`[PERFORMANCE] 🗺️ Coverage area: 10km radius (${Math.PI * 10000 * 10000 / 1000000} km²)`);

    // Filter and categorize landuse features for detailed statistics
    const allFeatures = this.landuseCache?.data?.features || [];
    const validFeatures = allFeatures.filter(f => f && f.properties && LANDUSE_TYPE_MAPPING[f.properties.value]);
    const featureTypes = validFeatures.reduce((acc, f) => {
      const type = f.properties.value;
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    logger.info(`[LANDUSE_CACHE] 📊 Feature analysis: ${validFeatures.length}/${allFeatures.length} valid features`);
    logger.info(`[LANDUSE_CACHE] 🏷️ Feature types: ${Object.entries(featureTypes).map(([type, count]) => `${type}(${count})`).join(', ')}`);

    // Forest detection analysis for large polygon capture effectiveness
    const forestFeatures = validFeatures.filter(f => f.properties.value === 'forest' || f.properties.value === 'wood');
    const largeForestFeatures = forestFeatures.filter(f => {
      // Estimate polygon size by bounding box area
      if (f.geometry && f.geometry.coordinates && f.geometry.coordinates[0]) {
        const coords = f.geometry.coordinates[0];
        const lats = coords.map(c => c[1]);
        const lngs = coords.map(c => c[0]);
        const latRange = Math.max(...lats) - Math.min(...lats);
        const lngRange = Math.max(...lngs) - Math.min(...lngs);
        const estimatedArea = latRange * lngRange; // Rough area estimate
        return estimatedArea > 0.001; // Large polygon threshold (≈0.11 km²)
      }
      return false;
    });

    // Debug: Log actual polygon sizes for first few forests
    forestFeatures.slice(0, 5).forEach((f, i) => {
      if (f.geometry && f.geometry.coordinates && f.geometry.coordinates[0]) {
        const coords = f.geometry.coordinates[0];
        const lats = coords.map(c => c[1]);
        const lngs = coords.map(c => c[0]);
        const latRange = Math.max(...lats) - Math.min(...lats);
        const lngRange = Math.max(...lngs) - Math.min(...lngs);
        const estimatedArea = latRange * lngRange;
        logger.debug(`[FOREST_DEBUG] Forest ${i+1}: ${f.properties.value}, area: ${estimatedArea.toFixed(6)}°² (${(estimatedArea * 111 * 111).toFixed(1)} km²)`);
      }
    });

    logger.info(`[FOREST_ANALYSIS] 🌲 Forest features detected: ${forestFeatures.length} total`);
    logger.info(`[FOREST_ANALYSIS] 🌳 Large forest polygons: ${largeForestFeatures.length} (area > 0.01°²)`);
    logger.info(`[FOREST_ANALYSIS] 📏 10km radius should capture very large forests extending far beyond immediate area`);

    // Store landuse cache in gameState for debug visualization
    gameState.landuseCache = this.landuseCache;

    // Determine if we should use directional spawning
    let useDirectionalSpawn = false;
    let movementAzimuth = null;

    // Check if we've moved at least 250m from the last spawn point
    if (gameState.lastSpawnLatLng && (lat !== gameState.lastSpawnLatLng.lat || lng !== gameState.lastSpawnLatLng.lng)) {
      try {
        // Calculate distance from last spawn point
        const dist = distanceMeters(lat, lng, gameState.lastSpawnLatLng.lat, gameState.lastSpawnLatLng.lng);

        // Only use directional spawning if we've moved at least 250m
        if (dist >= config.pokemon.spawnDistanceTrigger) {
          // Calculate bearing from last spawn point to current position
          movementAzimuth = turf.bearing(
            [gameState.lastSpawnLatLng.lng, gameState.lastSpawnLatLng.lat],
            [lng, lat]
          );
          // Normalize to 0-360 degrees
          movementAzimuth = (movementAzimuth + 360) % 360;
          useDirectionalSpawn = true;

          logger.debug(`Using directional spawn with azimuth: ${movementAzimuth}° (moved ${dist}m)`);
        }
      } catch (e) {
        logger.error('Error calculating direction for random Pokemon spawn:', e);
        useDirectionalSpawn = false;
      }
    }

    // 3. Collect all Pokemon data with grid-based distribution (time-consuming operations)
    logger.info(`[STANDARD_SPAWN] 🎯 Starting intelligent spawn collection for ${count} Pokemon with landuse optimization...`);
    const pokemonDataPromises = [];
    const cellCounts = new Map(); // Track spawns per grid cell
    const maxPokemonPerCell = 5;
    let totalAttempts = 0;
    let landuseSuccesses = 0;
    let landuseFallbacks = 0;
    let landuseFails = 0;

    // Import grid function
    const { getChainIndexForLocation } = await import('../pokemon-grid.js');

    // Simplified approach: Generate normal spawns and accept landuse when available
    const generateStandardSpawn = async () => {
      totalAttempts++;
      const candidateData = await this.collectPokemonData(lat, lng, useDirectionalSpawn, movementAzimuth, pokedexSnapshot);

      if (candidateData && candidateData.spawnLocation) {
        // Check if this spawn has landuse data
        if (candidateData.landuseData && candidateData.landuseData.value) {
          if (candidateData.landuseData.fallback) {
            landuseFallbacks++;
            logger.debug(`[LANDUSE_FALLBACK] 🎯 Standard spawn with fallback landuse: ${candidateData.landuseData.value}`);
          } else {
            landuseSuccesses++;
            // Special logging for forest detection success
            if (candidateData.landuseData.value === 'forest' || candidateData.landuseData.value === 'wood') {
              logger.info(`[FOREST_SUCCESS] 🌲 Large forest polygon captured! ${candidateData.landuseData.value} at ${candidateData.spawnLocation.lat.toFixed(6)}, ${candidateData.spawnLocation.lng.toFixed(6)}`);
            }
            logger.debug(`[LANDUSE_SUCCESS] ✅ Standard spawn with real landuse: ${candidateData.landuseData.value}`);
          }
        } else {
          landuseFails++;
          logger.debug(`[LANDUSE_MISS] ⚪ Standard spawn without landuse (should not happen with fallback)`);
        }
        return candidateData;
      }

      return null;
    };

    // Collect Pokemon with grid distribution (simplified approach)
    let attempts = 0;
    const maxAttempts = count * 5; // Reasonable max attempts

    while (pokemonDataPromises.length < count && attempts < maxAttempts) {
      attempts++;

      try {
        const candidateData = await generateStandardSpawn();

        if (candidateData && candidateData.spawnLocation) {
          // Check grid distribution
          const gridIndex = getChainIndexForLocation(
            candidateData.spawnLocation.lat,
            candidateData.spawnLocation.lng,
            gameState.chainsList.length
          );
          const currentCount = cellCounts.get(gridIndex) || 0;

          if (currentCount < maxPokemonPerCell) {
            // Accept this spawn
            pokemonDataPromises.push(Promise.resolve(candidateData));
            cellCounts.set(gridIndex, currentCount + 1);
            logger.debug(`[GRID] Accepted spawn in grid ${gridIndex} (${currentCount + 1}/${maxPokemonPerCell})`);
          } else {
            logger.debug(`[GRID] Rejected spawn in grid ${gridIndex} - cell full, retrying...`);
            // Don't increment pokemonDataPromises.length, try again
          }
        } else {
          logger.warn(`[SPAWN_ERROR] Failed to get valid spawn data on attempt ${attempts}`);
        }
      } catch (e) {
        logger.warn('Error in standard spawn collection:', e);
      }
    }

    // If we couldn't fill all slots, fill remaining slots normally (fallback)
    while (pokemonDataPromises.length < count) {
      logger.debug(`[FALLBACK] Filling remaining slot ${pokemonDataPromises.length + 1}/${count} with normal spawn`);
      pokemonDataPromises.push(this.collectPokemonData(lat, lng, useDirectionalSpawn, movementAzimuth, pokedexSnapshot));
      totalAttempts++;
      landuseFails++; // Count fallback spawns as landuse misses
    }

    // Wait for all data collection to complete (parallel)
    const pokemonDataResults = await Promise.all(pokemonDataPromises);

    logger.debug(`Grid distribution summary: ${cellCounts.size} cells used, max per cell: ${Math.max(...cellCounts.values())}`);
    for (const [gridIndex, count] of cellCounts.entries()) {
      logger.debug(`  Grid ${gridIndex}: ${count} Pokemon`);
    }

    // 3. Create Pokemon objects sequentially with pre-calculated data (fast, no race conditions)
    const finalizedPokemon = [];
    for (const pokemonData of pokemonDataResults) {
      if (pokemonData) {
        const finalPokemon = this.createFinalPokemon(pokemonData);
        if (finalPokemon) {
          finalizedPokemon.push(finalPokemon);
          gameState.addPokemon(finalPokemon);
        }
      }
    }

    // Log comprehensive landuse optimization statistics with 5km radius analysis
    logger.info(`[LANDUSE_STATS] 📊 Standard spawn results with 5km landuse coverage + fallback:`);
    logger.info(`[LANDUSE_STATS] 🎯 Total spawn attempts: ${totalAttempts}`);
    logger.info(`[LANDUSE_STATS] ✅ Real landuse spawns: ${landuseSuccesses}/${count} (${Math.round(landuseSuccesses/count*100)}%)`);
    logger.info(`[LANDUSE_STATS] 🎯 Fallback landuse spawns: ${landuseFallbacks}/${count} (${Math.round(landuseFallbacks/count*100)}%)`);
    logger.info(`[LANDUSE_STATS] ⚪ No landuse spawns: ${landuseFails}/${count} (${Math.round(landuseFails/count*100)}%)`);
    logger.info(`[LANDUSE_STATS] 🌍 Total with landuse: ${landuseSuccesses + landuseFallbacks}/${count} (${Math.round((landuseSuccesses + landuseFallbacks)/count*100)}%)`);
    logger.info(`[LANDUSE_STATS] ⚡ Average attempts per spawn: ${(totalAttempts/count).toFixed(1)}`);
    logger.info(`[LANDUSE_STATS] 🗺️ Coverage area: 10km radius (${Math.PI * 10000 * 10000 / 1000000} km²)`);

    // Effectiveness analysis for large polygon capture
    const realLandusePercentage = Math.round(landuseSuccesses/count*100);
    const fallbackPercentage = Math.round(landuseFallbacks/count*100);
    logger.info(`[EFFECTIVENESS] 🌲 Large polygon capture analysis (10km radius):`);
    logger.info(`[EFFECTIVENESS] 📈 Real OSM data coverage: ${realLandusePercentage}% (target: >70% for forest areas with 10km radius)`);
    logger.info(`[EFFECTIVENESS] 📉 Fallback dependency: ${fallbackPercentage}% (target: <30% with 10km radius)`);
    logger.info(`[EFFECTIVENESS] 🎯 Expected improvement: Very large forest polygons should now be captured`);
    logger.info(`[EFFECTIVENESS] 📊 Performance impact: 10km radius = ${Math.PI * 10000 * 10000 / 1000000} km² coverage area`);

    logger.info(`[STANDARD_SPAWN] ✅ Successfully created ${finalizedPokemon.length} standard Pokemon with 10km landuse coverage + fallback`);
    return finalizedPokemon;
  }

  /**
   * Get the base Pokemon for a grid cell
   * @param {number} lat - Latitude
   * @param {number} lon - Longitude
   * @returns {Object|null} - The base Pokemon or null if not found
   */
  getBasePokemonForGrid(lat, lon) {
    if (!gameState.chainsList.length) return null;
    const chainIdx = getChainIndexForLocation(lat, lon, gameState.chainsList.length);
    const chainId = gameState.chainsList[chainIdx];

    // Get the base Pokemon for this chain
    const basePokemon = gameState.basePokemonByChain[chainId];
    if (!basePokemon) return null;

    logger.debug(`Found base Pokemon for chain ${chainId}: ${basePokemon.name} (${basePokemon.dex_number})`);
    return basePokemon;
  }

  /**
   * Select a rarity based on the defined probabilities
   * @returns {string} - The selected rarity (common, scarce, rare, legendary, mythical)
   */
  selectRarity() {
    const random = Math.random();
    let cumulativeProbability = 0;

    for (const [rarity, probability] of Object.entries(this.rarityProbabilities)) {
      cumulativeProbability += probability;
      if (random <= cumulativeProbability) {
        return rarity;
      }
    }

    // Fallback to common if something goes wrong
    return 'common';
  }

  /**
   * Get a spawn level for a wild Pokemon based on the player's team
   * Uses the spawn-levels.js module to calculate the level
   * @returns {Promise<number>} - The spawn level (minimum 1)
   */
  async getSpawnLevel() {
    try {
      // Use the imported function from spawn-levels.js
      return await calculateSpawnLevel();
    } catch (e) {
      logger.error('Error determining spawn level:', e);
      // Fallback to a random level between 1-10 if there's an error
      const fallbackLevel = Math.floor(Math.random() * 10) + 1;
      logger.debug(`Using fallback spawn level: ${fallbackLevel}`);
      return fallbackLevel;
    }
  }

  /**
   * Spawn special Pokemon based on land use (with isolated data collection)
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @param {number} count - Number of Pokemon to spawn
   * @returns {Promise<Array>} - The spawned Pokemon
   */
  async spawnLanduseSpecialPokemons(lat, lng, count = this.landuseSpecialBatchSize) {
    logger.info(`[LANDUSE_SPAWN] 🏞️ Starting spawnLanduseSpecialPokemons with count=${count} at ${lat},${lng}`);
    try {
      // 1. Create Pokedex snapshot to prevent race conditions
      const pokedexSnapshot = JSON.parse(JSON.stringify(gameState.pokedexData));
      logger.debug(`Created pokedex snapshot for landuse spawning with ${pokedexSnapshot.length} entries`);

      // 2. Use the same comprehensive 10km landuse cache as standard spawns
      const landuseRadius = 10000; // Same 10km radius for consistency with large polygon capture
      const cacheKey = `${Math.round(lat * 1000)}:${Math.round(lng * 1000)}:${landuseRadius}`;
      let geojson;
      if (!this.landuseCache || this.landuseCache.key !== cacheKey) {
        logger.debug(`[LANDUSE_SPECIAL] 🗺️ Loading 10km landuse cache for special spawning: ${lat}, ${lng}`);
        geojson = await this.loadLanduseDataForArea(lat, lng, landuseRadius);
      } else {
        logger.debug(`[LANDUSE_SPECIAL] 💾 Using existing 10km landuse cache for special spawning: ${cacheKey}`);
        geojson = this.landuseCache.data;
      }

      // Filter features to only include those within reasonable spawn distance (1km radius)
      const spawnRadius = 1000; // 1km radius for special spawns (not the full 5km cache)
      const playerPoint = turf.point([lng, lat]);
      const nearbyFeatures = geojson.features.filter(f => {
        if (!f || !f.properties || !LANDUSE_TYPE_MAPPING[f.properties.value]) return false;

        try {
          // Check if feature is within spawn radius
          const featureCenter = turf.centroid(f);
          const distance = turf.distance(playerPoint, featureCenter, {units: 'meters'});
          return distance <= spawnRadius;
        } catch (e) {
          return false; // Skip invalid features
        }
      });

      logger.info(`[LANDUSE_SPECIAL] 📍 Filtered to ${nearbyFeatures.length}/${geojson.features.length} features within ${spawnRadius}m of player`);

      if (!nearbyFeatures.length) {
        logger.debug('No nearby landuse features found for special spawning');
        return [];
      }

      const features = nearbyFeatures;

      // 3. Collect all Pokemon data with grid-based distribution (time-consuming operations)
      logger.debug(`Starting parallel landuse data collection for ${count} Pokemon with grid distribution...`);
      const pokemonDataPromises = [];
      const cellCounts = new Map(); // Track spawns per grid cell
      const maxPokemonPerCell = 5;
      let attempts = 0;
      const maxAttempts = count * 3; // Prevent infinite loops

      // Import grid function
      const { getChainIndexForLocation } = await import('../pokemon-grid.js');

      while (pokemonDataPromises.length < count && attempts < maxAttempts) {
        attempts++;

        // Generate a spawn candidate
        const candidatePromise = this.collectLandusePokemonData(features, pokedexSnapshot);

        // Get the spawn location to determine grid index
        try {
          const candidateData = await candidatePromise;
          if (candidateData && candidateData.spawnLocation) {
            const gridIndex = getChainIndexForLocation(
              candidateData.spawnLocation.lat,
              candidateData.spawnLocation.lng,
              gameState.chainsList.length
            );

            // Check if this grid cell has room for more Pokemon
            const currentCount = cellCounts.get(gridIndex) || 0;
            if (currentCount < maxPokemonPerCell) {
              // Accept this spawn
              pokemonDataPromises.push(Promise.resolve(candidateData));
              cellCounts.set(gridIndex, currentCount + 1);
              logger.debug(`Accepted landuse spawn in grid ${gridIndex} (${currentCount + 1}/${maxPokemonPerCell})`);
            } else {
              logger.debug(`Rejected landuse spawn in grid ${gridIndex} - cell full (${currentCount}/${maxPokemonPerCell})`);
            }
          }
        } catch (e) {
          logger.warn('Error evaluating landuse spawn candidate for grid distribution:', e);
        }
      }

      // If we couldn't fill all slots with grid distribution, fill remaining slots normally
      while (pokemonDataPromises.length < count) {
        pokemonDataPromises.push(this.collectLandusePokemonData(features, pokedexSnapshot));
      }

      // Wait for all data collection to complete (parallel)
      const pokemonDataResults = await Promise.all(pokemonDataPromises);

      logger.debug(`Landuse grid distribution summary: ${cellCounts.size} cells used, max per cell: ${Math.max(...cellCounts.values())}`);
      for (const [gridIndex, count] of cellCounts.entries()) {
        logger.debug(`  Landuse Grid ${gridIndex}: ${count} Pokemon`);
      }

      // 3. Create Pokemon objects sequentially with pre-calculated data (fast, no race conditions)
      const spawned = [];
      for (const pokemonData of pokemonDataResults) {
        if (pokemonData) {
          const finalPokemon = this.createFinalLandusePokemon(pokemonData);
          if (finalPokemon) {
            spawned.push(finalPokemon);
            gameState.addPokemon(finalPokemon);
          }
        }
      }

      logger.info(`[LANDUSE_SPAWN] ✅ Successfully created ${spawned.length} landuse Pokemon with isolated spawning system`);
      return spawned;
    } catch (e) {
      logger.error('Error spawning landuse special Pokemon:', e);
      return [];
    }
  }

  /**
   * Collect landuse Pokemon data for spawning (parallel data collection)
   * @param {Array} features - Landuse features
   * @param {Array} pokedexSnapshot - Isolated pokedex data snapshot
   * @returns {Promise<Object|null>} - Collected landuse Pokemon data or null if failed
   */
  async collectLandusePokemonData(features, pokedexSnapshot) {
    try {
      // Helper function: Pick a feature by weight
      function pickFeatureByWeight(features, weights) {
        const r = Math.random();
        let acc = 0;
        for (let i = 0; i < features.length; i++) {
          acc += weights[i];
          if (r <= acc) return features[i];
        }
        return features[features.length - 1];
      }

      // Calculate weights for feature selection (simplified for parallel processing)
      const areas = features.map(f => turf.area(f));
      const alpha = 0.2; // Weighting factor for area
      let weights = areas.map(a => Math.pow(1 / (a || 1), alpha));

      // Normalize weights
      const sum = weights.reduce((a, b) => a + b, 0);
      const normWeights = weights.map(w => w / sum);

      // Pick a polygon by weight
      const feature = pickFeatureByWeight(features, normWeights);
      if (!feature) return null;

      const landuseType = feature.properties.value;
      const mapping = LANDUSE_TYPE_MAPPING[landuseType];
      if (!mapping || !mapping.type) return null;

      // Get all Pokémon of this type from the pokedex
      const allTypeMatches = pokedexSnapshot.filter(
        p => p.types && p.types.includes(mapping.type)
      );
      if (!allTypeMatches || allTypeMatches.length === 0) return null;

      // Select a rarity based on the defined probabilities
      const targetRarity = this.selectRarity();

      // Try to find Pokémon with the selected rarity and matching type
      let candidates = allTypeMatches.filter(p => p.rarity === targetRarity);

      // If no Pokémon with the selected rarity is available, try other rarities
      if (!candidates || candidates.length === 0) {
        // Define fallback order based on the selected rarity
        let fallbackRarities;
        switch (targetRarity) {
          case 'mythical':
            fallbackRarities = ['legendary', 'rare', 'scarce', 'common'];
            break;
          case 'legendary':
            fallbackRarities = ['rare', 'scarce', 'common', 'mythical'];
            break;
          case 'rare':
            fallbackRarities = ['scarce', 'common', 'legendary', 'mythical'];
            break;
          case 'scarce':
            fallbackRarities = ['common', 'rare', 'legendary', 'mythical'];
            break;
          case 'common':
          default:
            fallbackRarities = ['scarce', 'rare', 'legendary', 'mythical'];
            break;
        }

        // Try each fallback rarity in order
        for (const fallbackRarity of fallbackRarities) {
          candidates = allTypeMatches.filter(p => p.rarity === fallbackRarity);
          if (candidates && candidates.length > 0) {
            break;
          }
        }

        // If still no candidates, use all Pokémon of this type regardless of rarity
        if (!candidates || candidates.length === 0) {
          candidates = allTypeMatches;
        }
      }

      if (!candidates || candidates.length === 0) return null;

      // Generate a random point in the polygon
      let testLng, testLat;

      try {
        // First try to generate a random point within the polygon
        const spawnPoint = turf.randomPoint(1, {bbox: turf.bbox(feature)}).features[0];

        // Check if point is actually in the polygon, otherwise use a point on the feature
        if (turf.booleanPointInPolygon(spawnPoint, feature)) {
          [testLng, testLat] = spawnPoint.geometry.coordinates;
        } else {
          // If random point is outside polygon, use a point on the feature
          const pointOnFeature = turf.pointOnFeature(feature);
          [testLng, testLat] = pointOnFeature.geometry.coordinates;
        }
      } catch (e) {
        try {
          // Fallback to pointOnFeature
          const pointOnFeature = turf.pointOnFeature(feature);
          [testLng, testLat] = pointOnFeature.geometry.coordinates;
        } catch (e2) {
          // Ultimate fallback - skip this spawn
          return null;
        }
      }

      // Choose a random Pokemon from the candidates
      const pokeEntry = candidates[Math.floor(Math.random() * candidates.length)];

      // Get spawn level based on player's team average level
      const level = await this.getSpawnLevel();

      // Calculate evolution data with isolated pokedex snapshot
      const evolutionData = await this.calculateEvolutionData(pokeEntry.name, level, pokedexSnapshot);

      // Return all collected data
      return {
        basePokeEntry: pokeEntry,
        level,
        evolutionData,
        spawnLocation: { lat: testLat, lng: testLng },
        landuseData: {
          value: landuseType,
          mapping: mapping,
          feature: feature
        },
        timestamp: Date.now() // For debugging
      };
    } catch (e) {
      logger.error('Error collecting landuse Pokemon data:', e);
      return null;
    }
  }

  /**
   * Create final landuse Pokemon object with pre-calculated data
   * @param {Object} pokemonData - Pre-calculated Pokemon data
   * @returns {Pokemon|null} - Final Pokemon object or null
   */
  createFinalLandusePokemon(pokemonData) {
    return this.createFinalPokemonBase(pokemonData, true);
  }


}
