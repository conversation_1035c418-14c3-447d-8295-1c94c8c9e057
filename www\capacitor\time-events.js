// capacitor/time-events.js
// Time-based event system for managing Pokemon spawns across app sessions

import { logger } from '../utils/logger.js';
import { storageService } from '../storage/storage-service.js';
import { config } from '../config.js';
import { gameState } from '../state/game-state.js';

// Storage keys from config
const SPAWN_STORAGE_KEY = config.storage.timeEventSpawnsKey;
const LAST_HOUR_KEY = config.storage.lastHourKey;

/**
 * Get the current hour time slot (0-23)
 * @returns {number} - The current hour (0-23)
 */
export function getCurrentHourSlot() {
  return new Date().getHours();
}

/**
 * Check if we're in a new time slot compared to the stored one
 * @returns {Promise<boolean>} - True if we're in a new time slot
 */
export async function isNewTimeSlot() {
  const currentHour = getCurrentHourSlot();
  const lastHour = await storageService.get(LAST_HOUR_KEY, null);

  // If no last hour is stored or the hour has changed
  return lastHour === null || lastHour !== currentHour;
}

/**
 * Save the current time slot to storage
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function saveCurrentTimeSlot() {
  const currentHour = getCurrentHourSlot();
  return await storageService.set(LAST_HOUR_KEY, currentHour);
}

/**
 * Save Pokemon spawns to storage
 * @param {Array} pokemons - The Pokemon to save
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function saveSpawnsToStorage(pokemons) {
  // Create a simplified version of the Pokemon objects for storage
  // We need to store all essential data needed to recreate them properly
  const simplifiedPokemons = pokemons.map(pokemon => ({
    // Basic identification
    id: pokemon.id,

    // Name properties
    base_name: pokemon.base_name,
    name: pokemon.name,

    // Type information
    type: pokemon.type,
    types: pokemon.types,

    // Level and experience
    level: pokemon.level,
    experience: pokemon.experience,
    _experience: pokemon._experience,

    // Evolution data
    evolution_level: pokemon.evolution_level,
    evolution_item: pokemon.evolution_item,
    evolution_chain_id: pokemon.evolution_chain_id,

    // Visual data
    dex_number: pokemon.dex_number,
    base_sprite: pokemon.base_sprite,
    image_url: pokemon.image_url,
    image: pokemon.image,

    // Spawn location
    spawnLat: pokemon.spawnLat,
    spawnLng: pokemon.spawnLng,
    lat: pokemon.lat,
    lng: pokemon.lng,

    // Landuse data
    landuseSpecial: pokemon.landuseSpecial,
    landuseType: pokemon.landuseType,
    landuseTypeName: pokemon.landuseTypeName,
    featureId: pokemon.featureId,

    // Metadata
    rarity: pokemon.rarity || 'common',
    caughtAt: pokemon.caughtAt
  }));

  logger.debug(`Saving ${simplifiedPokemons.length} Pokemon spawns to storage with complete data`);
  return await storageService.set(SPAWN_STORAGE_KEY, simplifiedPokemons);
}

/**
 * Load Pokemon spawns from storage
 * @returns {Promise<Array>} - The loaded Pokemon spawns
 */
export async function loadSpawnsFromStorage() {
  const spawns = await storageService.get(SPAWN_STORAGE_KEY, []);
  logger.debug(`Loaded ${spawns.length} Pokemon spawns from storage`);
  return spawns;
}

/**
 * Clear Pokemon spawns from storage
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function clearSpawnsFromStorage() {
  logger.debug('Clearing Pokemon spawns from storage');
  return await storageService.remove(SPAWN_STORAGE_KEY);
}

/**
 * Initialize the time event system
 * @returns {Promise<{isNewSlot: boolean, spawns: Array}>} - Whether we're in a new time slot and any loaded spawns
 */
export async function initializeTimeEvents() {
  const newTimeSlot = await isNewTimeSlot();

  // If we're in a new time slot, clear the spawns and save the current time slot
  if (newTimeSlot) {
    await clearSpawnsFromStorage();
    await saveCurrentTimeSlot();
    return { isNewSlot: true, spawns: [] };
  }

  // If we're in the same time slot, load the spawns
  const spawns = await loadSpawnsFromStorage();
  return { isNewSlot: false, spawns };
}

/**
 * Remove a Pokemon from storage by ID
 * @param {string} id - The ID of the Pokemon to remove
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function removePokemonFromStorage(id) {
  const spawns = await loadSpawnsFromStorage();

  // Find the index of the Pokemon with the given ID
  const index = spawns.findIndex(p => p.id === id);

  // If the Pokemon is not found, return true (nothing to remove)
  if (index === -1) {
    logger.debug(`Pokemon with ID ${id} not found in storage`);
    return true;
  }

  // Remove the Pokemon from the array
  spawns.splice(index, 1);
  logger.debug(`Removed Pokemon with ID ${id} from storage, ${spawns.length} remaining`);

  // Save the updated array
  return await storageService.set(SPAWN_STORAGE_KEY, spawns);
}

/**
 * Synchronize the current game state Pokemon with storage
 * This ensures that storage always matches the current game state
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function syncGameStateWithStorage() {
  // Get all Pokemon from game state
  const { pokemons } = gameState;

  // Clean up old Pokemon before syncing (keep only last 50 to prevent storage overflow)
  const MAX_POKEMON_IN_STORAGE = 50;

  let pokemonsToStore = pokemons;
  if (pokemons.length > MAX_POKEMON_IN_STORAGE) {
    // Keep the most recent Pokemon (by spawn time)
    pokemonsToStore = pokemons
      .sort((a, b) => (b.spawnTime || 0) - (a.spawnTime || 0))
      .slice(0, MAX_POKEMON_IN_STORAGE);

    logger.warn(`Storage optimization: Keeping only ${MAX_POKEMON_IN_STORAGE} most recent Pokemon (had ${pokemons.length})`);
  }

  // Create highly compressed Pokemon objects for storage (minimize storage size)
  const simplifiedPokemons = pokemonsToStore.map(pokemon => ({
    // Essential identification only
    id: pokemon.id,
    name: pokemon.name,
    dex_number: pokemon.dex_number,
    level: pokemon.level,

    // Position (rounded to reduce precision and save space)
    lat: Math.round(pokemon.lat * 100000) / 100000, // ~1m precision
    lng: Math.round(pokemon.lng * 100000) / 100000,

    // Only store landuse data if it's special (conditional properties)
    ...(pokemon.landuseSpecial && {
      landuseSpecial: true,
      landuseType: pokemon.landuseType
    }),

    // Only store rarity if not common
    ...(pokemon.rarity && pokemon.rarity !== 'common' && {
      rarity: pokemon.rarity
    }),

    // Only store caught timestamp if caught
    ...(pokemon.caughtAt && {
      caughtAt: pokemon.caughtAt
    })
  }));

  logger.debug(`Syncing ${simplifiedPokemons.length} Pokemon with storage using complete data`);

  // Save the array to storage
  return await storageService.set(SPAWN_STORAGE_KEY, simplifiedPokemons);
}

/**
 * Add a new spawn to storage
 * @param {Object} pokemon - The Pokemon to add
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function addSpawnToStorage(pokemon) {
  // Instead of adding individual Pokemon, we'll sync the entire game state
  // This ensures consistency between game state and storage
  return await syncGameStateWithStorage();
}

/**
 * This function is no longer used.
 * We only check for time slot changes when the app starts, not while it's running.
 * This ensures that Pokemon spawns remain consistent during a single app session.
 */
export function setupHourChangeTimer() {
  // This function is intentionally empty
  // We don't want to check for hour changes while the app is running
  logger.debug('Hour change timer is disabled - time slots only change on app restart');
}
