// config.js
// Centralized configuration for the GPS Pokemon App

export const config = {
  map: {
    defaultCenter: [51.96, 7.62],
    defaultZoom: 13,
    tileUrl: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    attribution: '&copy; OpenStreetMap-Contributors'
  },
  pokemon: {
    spawnRadius: 500,
    spawnDistanceTrigger: 250,
    spawnBatchSize: 20,
    landuseSpecialBatchSize: 20,
    maxPokemons: 100,
    catchRadius: 50,
    moveInterval: 10000,
    moveAnimationDuration: 2000,
    moveAnimationSteps: 30
  },
  grid: {
    cellSizeMeters: 300
  },
  trainers: {
    spawnRadius: 500,           // Radius for trainer spawning in meters
    spawnDistanceTrigger: 250,  // Distance trigger for respawning trainers
    desiredCount: 5,            // Number of trainers to spawn
    minSpacing: 20,             // Minimum spacing between trainers in meters
    minSpacingFallback: 10,     // Fallback minimum spacing
    cacheTimeout: 60            // OSM ways cache timeout in minutes
  },
  geolocation: {
    enableHighAccuracy: true,
    timeout: 10000,
    maximumAge: 0
  },
  ui: {
    compassSize: 36,
    markerSize: 36,
    specialMarkerSize: 40,
    debugSectorAngle: 120,
    playerSizeWidth: 16,
    playerSizeHeight: 24,
    playerAnimationDuration: 500, // ms
    trainerSizeWidth: 16,
    trainerSizeHeight: 24,
    showCompassMarker: false,
    showPlayerSprite: true
  },
  storage: {
    pokedexKey: 'pokedex',
    encountersKey: 'pokemonEncounters',
    timeEventSpawnsKey: 'timeEventSpawns',
    lastHourKey: 'lastHourTimeslot',
    caughtPokemonKey: 'caughtPokemon',
    teamPokemonKey: 'teamPokemon'
  },
  timeEvents: {
    checkIntervalMs: 60000, // Check for hour changes every minute
    enabled: true
  }
};
