<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Battle Utils Consolidation Test - Working Version</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 20px;
            background-color: #1a1a1a;
            color: #00ff00;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #ffff00;
            text-align: center;
        }
        .test-output {
            background-color: #000;
            border: 1px solid #333;
            padding: 20px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-size: 14px;
            max-height: 600px;
            overflow-y: auto;
        }
        .run-button {
            background-color: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .run-button:hover {
            background-color: #0052a3;
        }
        .status {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background-color: #004d00;
            color: #00ff00;
        }
        .status.error {
            background-color: #4d0000;
            color: #ff0000;
        }
        .status.info {
            background-color: #003d4d;
            color: #00ccff;
        }
        .pokemon-exp-container {
            background-color: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .pokemon-exp-bar {
            width: 100%;
            height: 20px;
            background-color: #666;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        .pokemon-exp-fill {
            height: 100%;
            background-color: #4a90e2;
            border-radius: 10px;
            transition: width 0.5s ease;
        }
        .pokemon-exp-new {
            position: absolute;
            top: 0;
            height: 100%;
            background-color: #4a90e2;
            border-radius: 10px;
            opacity: 0;
            transition: all 0.3s ease;
        }
        .pokemon-exp-text {
            text-align: center;
            margin-top: 5px;
            font-size: 12px;
            color: #ccc;
        }
        .level-up-notification {
            background-color: #ffd700;
            color: #000;
            padding: 5px 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            font-weight: bold;
            text-align: center;
        }
        .test-notification {
            background-color: #00ff00;
            color: #000;
            padding: 5px 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Battle Utils Consolidation Test - Working Version</h1>
        
        <div class="status info" id="status">
            Bereit zum Testen der konsolidierten Battle-Utility-Funktionen (ohne ES6-Module)
        </div>
        
        <button class="run-button" onclick="runTests()">Tests starten</button>
        
        <div class="test-output" id="output">
            Klicke auf "Tests starten" um die Konsolidierungstests auszuführen...
        </div>
    </div>

    <script>
        // Mock logger for testing
        const logger = {
            debug: (...args) => console.log('DEBUG:', ...args),
            error: (...args) => console.error('ERROR:', ...args)
        };

        // Mock experience system functions
        function getExpForLevel(level, curve) {
            switch (curve) {
                case 'fast':
                    return Math.floor((4 * Math.pow(level, 3)) / 5);
                case 'medium_fast':
                    return Math.pow(level, 3);
                case 'medium_slow':
                    return Math.floor((6 / 5) * Math.pow(level, 3) - 15 * Math.pow(level, 2) + 100 * level - 140);
                case 'slow':
                    return Math.floor((5 * Math.pow(level, 3)) / 4);
                case 'parabolic':
                    return Math.floor(1.2 * Math.pow(level, 3) - 10 * Math.pow(level, 2) + 90 * level);
                default:
                    return Math.pow(level, 3);
            }
        }

        function getExpCurveForRarity(rarity) {
            const curves = {
                mythical: 'slow',
                legendary: 'medium_slow',
                starter: 'medium_fast',
                rare: 'parabolic',
                scarce: 'fast',
                common: 'fast'
            };
            return curves[rarity] || 'fast';
        }

        function getPokemonDisplayName(pokemon) {
            // Simple German name mapping for testing
            const germanNames = {
                'Pikachu': 'Pikachu',
                'Charmander': 'Glumanda',
                'Squirtle': 'Schiggy'
            };
            return germanNames[pokemon.name] || pokemon.name;
        }

        // Consolidated utility functions (copied from battle-utils.js)
        function calculateExpProgress(pokemon, additionalExp = 0) {
            try {
                const result = {
                    currentExp: 0,
                    currentLevelExp: 0,
                    nextLevelExp: 0,
                    expInCurrentLevel: 0,
                    expNeededForNextLevel: 0,
                    progressPercentage: 0,
                    newExpPercentage: 0,
                    willLevelUp: false
                };

                if (!pokemon || typeof pokemon.level !== 'number') {
                    return result;
                }

                const level = pokemon.level || 1;
                const nextLevel = level + 1;
                const rarity = pokemon.rarity || 'common';
                const curve = getExpCurveForRarity(rarity);

                const currentLevelExp = getExpForLevel(level, curve);
                const nextLevelExp = getExpForLevel(nextLevel, curve);
                const currentExp = typeof pokemon.experience === 'number' ? pokemon.experience : currentLevelExp;

                const expInCurrentLevel = currentExp - currentLevelExp;
                const expNeededForNextLevel = nextLevelExp - currentLevelExp;
                const progressPercentage = Math.min(100, Math.floor((expInCurrentLevel / expNeededForNextLevel) * 100));

                const newExp = currentExp + additionalExp;
                const newExpInLevel = Math.min(newExp - currentLevelExp, expNeededForNextLevel);
                const newProgressPercentage = Math.min(100, Math.floor((newExpInLevel / expNeededForNextLevel) * 100));
                const willLevelUp = newExp >= nextLevelExp;

                result.currentExp = currentExp;
                result.currentLevelExp = currentLevelExp;
                result.nextLevelExp = nextLevelExp;
                result.expInCurrentLevel = expInCurrentLevel;
                result.expNeededForNextLevel = expNeededForNextLevel;
                result.progressPercentage = progressPercentage;
                result.newExpPercentage = newProgressPercentage;
                result.willLevelUp = willLevelUp;

                return result;
            } catch (e) {
                logger.error('Error in calculateExpProgress:', e);
                return {
                    currentExp: 0,
                    currentLevelExp: 0,
                    nextLevelExp: 0,
                    expInCurrentLevel: 0,
                    expNeededForNextLevel: 0,
                    progressPercentage: 0,
                    newExpPercentage: 0,
                    willLevelUp: false
                };
            }
        }

        function animateExpBar(container, expProgress, expGained) {
            try {
                const expFill = container.querySelector('.pokemon-exp-fill');
                const expNew = container.querySelector('.pokemon-exp-new');
                const expText = container.querySelector('.pokemon-exp-text');

                if (!expFill || !expNew || !expText) {
                    logger.debug('XP animation elements not found:', { expFill: !!expFill, expNew: !!expNew, expText: !!expText });
                    return false;
                }

                const newExpInLevel = Math.min(
                    expProgress.expInCurrentLevel + expGained,
                    expProgress.expNeededForNextLevel
                );

                const newExpWidth = Math.min(100, Math.floor((expGained / expProgress.expNeededForNextLevel) * 100));
                const newPercentage = Math.min(100, Math.floor((newExpInLevel / expProgress.expNeededForNextLevel) * 100));

                if (expNew && newExpWidth > 0) {
                    expNew.style.width = `${newExpWidth}%`;
                    expNew.style.right = `${100 - expProgress.progressPercentage - newExpWidth}%`;
                    expNew.style.opacity = '0.6';
                    expNew.style.backgroundColor = '#4a90e2';
                }

                expText.textContent = `${newExpInLevel}/${expProgress.expNeededForNextLevel} XP`;

                setTimeout(() => {
                    if (expNew) {
                        expNew.style.right = `${100 - newPercentage}%`;
                    }
                    expFill.style.width = `${newPercentage}%`;
                    
                    setTimeout(() => {
                        if (expNew) {
                            expNew.style.opacity = '0';
                        }
                    }, 300);
                }, 500);

                return true;
            } catch (e) {
                logger.error('Error animating experience bar:', e);
                return false;
            }
        }

        function showNotification(container, message, className = 'notification', duration = 3000) {
            return new Promise((resolve) => {
                if (!container) {
                    resolve(false);
                    return;
                }

                const notification = document.createElement('div');
                notification.className = className;
                notification.textContent = message;
                notification.style.opacity = '1';

                container.appendChild(notification);

                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                    resolve(true);
                }, duration);
            });
        }

        function showLevelUpNotification(container, pokemon, oldLevel, newLevel) {
            return new Promise((resolve) => {
                if (!container) {
                    resolve(false);
                    return;
                }

                const message = `${getPokemonDisplayName(pokemon)} erreicht Level ${newLevel}!`;
                showNotification(container, message, 'level-up-notification', 3000).then(resolve);
                
                logger.debug(`${pokemon.name} leveled up from ${oldLevel} to ${newLevel}!`);
            });
        }

        // Test functions
        function createMockContainer() {
            const container = document.createElement('div');
            container.innerHTML = `
                <div class="pokemon-exp-container">
                    <div class="pokemon-exp-bar">
                        <div class="pokemon-exp-fill" style="width: 50%;"></div>
                        <div class="pokemon-exp-new" style="width: 0%; opacity: 0;"></div>
                    </div>
                    <div class="pokemon-exp-text">50/100 XP</div>
                </div>
            `;
            document.body.appendChild(container);
            return container;
        }

        function createTestPokemon() {
            return {
                name: 'Pikachu',
                level: 5,
                experience: 125,
                rarity: 'starter',
                dex_number: 25
            };
        }

        async function runTests() {
            const statusEl = document.getElementById('status');
            const outputEl = document.getElementById('output');
            
            statusEl.textContent = 'Tests werden ausgeführt...';
            statusEl.className = 'status info';
            
            let testOutput = '';
            let allTestsPassed = true;

            // Override console for capturing output
            const originalLog = console.log;
            const originalError = console.error;
            
            console.log = (...args) => {
                testOutput += args.join(' ') + '\n';
                originalLog.apply(console, args);
            };
            console.error = (...args) => {
                testOutput += 'ERROR: ' + args.join(' ') + '\n';
                originalError.apply(console, args);
            };

            try {
                testOutput += '🧪 Starting Battle Utils Consolidation Tests\n\n';

                // Test 1: calculateExpProgress
                testOutput += '=== Test 1: calculateExpProgress Function ===\n';
                const pokemon = createTestPokemon();
                const expProgress = calculateExpProgress(pokemon);
                
                testOutput += `Test Pokemon: ${JSON.stringify(pokemon)}\n`;
                testOutput += `Experience Progress: ${JSON.stringify(expProgress, null, 2)}\n`;
                
                if (expProgress.currentExp === pokemon.experience) {
                    testOutput += '✅ calculateExpProgress: Current XP correct\n';
                } else {
                    testOutput += '❌ calculateExpProgress: Current XP incorrect\n';
                    allTestsPassed = false;
                }

                // Test 2: animateExpBar
                testOutput += '\n=== Test 2: animateExpBar Function ===\n';
                const container = createMockContainer();
                const result = animateExpBar(container, expProgress, 50);
                
                if (result) {
                    testOutput += '✅ animateExpBar: Function executed successfully\n';
                    testOutput += '✅ animateExpBar: DOM elements found and modified\n';
                } else {
                    testOutput += '❌ animateExpBar: Function failed\n';
                    allTestsPassed = false;
                }

                // Test 3: showNotification
                testOutput += '\n=== Test 3: showNotification Function ===\n';
                const notificationResult = await showNotification(container, 'Test notification', 'test-notification', 1000);
                
                if (notificationResult) {
                    testOutput += '✅ showNotification: Notification created and displayed\n';
                } else {
                    testOutput += '❌ showNotification: Failed to create notification\n';
                    allTestsPassed = false;
                }

                // Test 4: showLevelUpNotification
                testOutput += '\n=== Test 4: showLevelUpNotification Function ===\n';
                const levelUpResult = await showLevelUpNotification(container, pokemon, 4, 5);
                
                if (levelUpResult) {
                    testOutput += '✅ showLevelUpNotification: Level up notification created\n';
                } else {
                    testOutput += '❌ showLevelUpNotification: Failed to create level up notification\n';
                    allTestsPassed = false;
                }

                // Clean up
                document.body.removeChild(container);

                // Test 5: Edge cases
                testOutput += '\n=== Test 5: Edge Cases ===\n';
                const nullResult = calculateExpProgress(null);
                if (nullResult.progressPercentage === 0) {
                    testOutput += '✅ Edge case: Null Pokemon handled correctly\n';
                } else {
                    testOutput += '❌ Edge case: Null Pokemon not handled correctly\n';
                    allTestsPassed = false;
                }

                const nullNotification = await showNotification(null, 'test', 'test', 100);
                if (nullNotification === false) {
                    testOutput += '✅ Edge case: Null container handled correctly\n';
                } else {
                    testOutput += '❌ Edge case: Null container not handled correctly\n';
                    allTestsPassed = false;
                }

                testOutput += '\n=== Consolidation Summary ===\n';
                testOutput += '✅ animateExpBar() - Successfully consolidated from both battle screens\n';
                testOutput += '✅ showLevelUpNotification() - Successfully consolidated from TrainerBattleScreen\n';
                testOutput += '✅ calculateExpProgress() - Already unified in previous task\n';
                testOutput += '✅ showNotification() - Already available in battle-utils.js\n';

                if (allTestsPassed) {
                    testOutput += '\n🎉 All consolidation tests passed!\n';
                    testOutput += 'Battle utility functions are now properly consolidated.\n';
                    statusEl.textContent = '✅ Alle Tests erfolgreich! Battle-Utility-Funktionen sind konsolidiert.';
                    statusEl.className = 'status success';
                } else {
                    testOutput += '\n❌ Some tests failed. Please check the implementation.\n';
                    statusEl.textContent = '⚠️ Einige Tests fehlgeschlagen. Bitte Implementation prüfen.';
                    statusEl.className = 'status error';
                }

            } catch (error) {
                testOutput += `\n❌ Test execution failed: ${error.message}\n`;
                statusEl.textContent = `❌ Fehler beim Ausführen der Tests: ${error.message}`;
                statusEl.className = 'status error';
                allTestsPassed = false;
            }

            // Restore console
            console.log = originalLog;
            console.error = originalError;

            outputEl.textContent = testOutput;
        }
    </script>
</body>
</html>
